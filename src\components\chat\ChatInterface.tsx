
import React, { useState, useRef, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { WelcomeScreen } from './WelcomeScreen';
import { AIStatusIndicator } from './AIStatusIndicator';
import { usePuterAI } from '@/hooks/usePuterAI';
import { usePuterAuth } from '@/hooks/usePuterAuth';
import { usePuterStorage } from '@/hooks/usePuterStorage';
import { toast } from 'sonner';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  model?: string;
  images?: string[];
  isStreaming?: boolean;
}

interface ChatInterfaceProps {
  selectedModel: string;
  currentChatId: string | null;
  onNewMessage: (message: Message) => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  selectedModel,
  currentChatId,
  onNewMessage
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [streamingMessage, setStreamingMessage] = useState<Message | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const { sendMessage, isLoading, setIsLoading } = usePuterAI();
  const { isSignedIn, signIn } = usePuterAuth();
  const { saveChat, loadChat } = usePuterStorage();

  const handleSendMessage = async (content: string, images?: File[], options?: any) => {
    if (!isSignedIn) {
      try {
        await signIn();
      } catch (error) {
        toast.error('Please sign in to use the chat');
        return;
      }
    }

    // Convert images to URLs for display
    const imageUrls = images?.map(img => URL.createObjectURL(img)) || [];

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      role: 'user',
      timestamp: new Date(),
      images: imageUrls,
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    onNewMessage(userMessage);

    try {
      // Prepare messages for AI
      const chatMessages = updatedMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      // Prepare image URLs for vision models
      let imageURLs: string[] | undefined;
      if (images && images.length > 0) {
        // Convert File objects to URLs for API
        imageURLs = await Promise.all(
          images.map(async (file) => {
            return new Promise<string>((resolve) => {
              const reader = new FileReader();
              reader.onload = (e) => resolve(e.target?.result as string);
              reader.readAsDataURL(file);
            });
          })
        );
      }

      const aiOptions = {
        model: selectedModel,
        stream: options?.stream || false,
        testMode: options?.testMode || false,
      };

      if (aiOptions.stream) {
        // Handle streaming response
        setIsLoading(true);
        const streamingMessageId = (Date.now() + 1).toString();
        const initialStreamingMessage: Message = {
          id: streamingMessageId,
          content: '',
          role: 'assistant',
          timestamp: new Date(),
          model: selectedModel,
          isStreaming: true,
        };

        setStreamingMessage(initialStreamingMessage);

        try {
          const response = await sendMessage(chatMessages, aiOptions, imageURLs);
          let fullContent = '';

          for await (const part of response) {
            const text = part?.text || part?.message?.content || '';
            if (text) {
              fullContent += text;
              setStreamingMessage(prev => prev ? {
                ...prev,
                content: fullContent,
              } : null);
            }
          }

          // Finalize the streaming message
          const finalMessage: Message = {
            ...initialStreamingMessage,
            content: fullContent,
            isStreaming: false,
          };

          const finalMessages = [...updatedMessages, finalMessage];
          setMessages(finalMessages);
          setStreamingMessage(null);
          onNewMessage(finalMessage);

          // Save chat to storage
          if (currentChatId) {
            await saveChat(currentChatId, {
              messages: finalMessages,
              title: content.slice(0, 50),
              updatedAt: new Date().toISOString(),
            });
          }
        } catch (error) {
          setStreamingMessage(null);
          throw error;
        }
      } else {
        // Handle non-streaming response
        setIsLoading(true);

        // Check AI service availability
        const hasRealAI = !!(window as any).puter?.ai?.chat;
        console.log(`🤖 AI Service Status: ${hasRealAI ? 'Connected' : 'Demo Mode'}`);

        const response = await sendMessage(chatMessages, aiOptions, imageURLs);

        // Determine response type and add status indicators
        console.log('🔍 Raw response received:', response);

        let responseContent = '';
        if (typeof response === 'string') {
          responseContent = response;
        } else if (response?.message) {
          // Handle different message formats
          if (typeof response.message === 'string') {
            responseContent = response.message;
          } else if (response.message?.content) {
            responseContent = response.message.content;
          } else if (response.message?.text) {
            responseContent = response.message.text;
          } else {
            // If message is an object, try to extract meaningful content
            console.log('🔍 Message object:', response.message);
            responseContent = response.message.toString?.() || JSON.stringify(response.message, null, 2);
          }
        } else if (response?.content) {
          responseContent = response.content;
        } else if (response?.text) {
          responseContent = response.text;
        } else {
          responseContent = 'I apologize, but I encountered an issue processing your request.';
        }

        const isEnhanced = response?.enhanced || false;
        const isDemoMode = !hasRealAI;

        // Add appropriate status indicators
        if (isDemoMode && !responseContent.includes('Demo Mode')) {
          responseContent = `🎭 **Demo Mode Active** - This is an intelligent demo response.\n\n${responseContent}`;
        } else if (isEnhanced) {
          responseContent = `🤖 **AI Enhanced Response**\n\n${responseContent}`;
        } else if (hasRealAI) {
          responseContent = `🤖 **Live AI Response**\n\n${responseContent}`;
        }

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: responseContent,
          role: 'assistant',
          timestamp: new Date(),
          model: selectedModel,
        };

        const finalMessages = [...updatedMessages, assistantMessage];
        setMessages(finalMessages);
        onNewMessage(assistantMessage);

        // Save chat to storage
        if (currentChatId) {
          await saveChat(currentChatId, {
            messages: finalMessages,
            title: content.slice(0, 50),
            updatedAt: new Date().toISOString(),
          });
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load chat when currentChatId changes
  useEffect(() => {
    if (currentChatId) {
      loadChat(currentChatId).then((chatData) => {
        if (chatData && chatData.messages) {
          setMessages(chatData.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })));
        }
      }).catch(console.error);
    } else {
      setMessages([]);
    }
  }, [currentChatId, loadChat]);

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages, streamingMessage]);

  const allMessages = streamingMessage
    ? [...messages, streamingMessage]
    : messages;

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1 p-6" ref={scrollAreaRef}>
        {allMessages.length === 0 ? (
          <WelcomeScreen />
        ) : (
          <div className="space-y-6 max-w-4xl mx-auto">
            {allMessages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            {isLoading && !streamingMessage && (
              <div className="flex justify-start">
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 max-w-3xl">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-150"></div>
                    <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-300"></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </ScrollArea>

      <div className="p-6 border-t border-white/20">
        <div className="flex items-center justify-between mb-4">
          <AIStatusIndicator />
          <div className="text-xs text-slate-400">
            {selectedModel && `Model: ${selectedModel}`}
          </div>
        </div>
        <ChatInput onSendMessage={handleSendMessage} disabled={isLoading} />
      </div>
    </div>
  );
};
