
import { useState, useCallback } from 'react';

interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
}

interface FunctionDefinition {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: object;
    strict?: boolean;
  };
}

interface UsePuterAIOptions {
  model?: string;
  stream?: boolean;
  tools?: FunctionDefinition[];
}

export const usePuterAI = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(async (
    promptOrMessages: string | ChatMessage[],
    options: UsePuterAIOptions & { testMode?: boolean } = {},
    imageURL?: string | string[]
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const { model = 'gpt-4o-mini', stream = false, tools, testMode = false } = options;

      // Log current mode for debugging
      console.log(`🔍 AI Request Mode: ${testMode ? 'TEST/DEMO' : 'REAL'}, Model: ${model}`);

      // If test mode is explicitly enabled, skip real AI and go to demo
      if (testMode) {
        console.log('🎭 Test mode enabled - using demo responses');
        return generateDemoResponse(promptOrMessages, options);
      }

      // First, try to use real AI if available
      if ((window as any).puter?.ai?.chat) {
        console.log('🤖 Attempting real Puter AI service...');
        console.log('🔍 Puter object:', (window as any).puter);
        console.log('🔍 AI service:', (window as any).puter.ai);

        let response;

        if (imageURL) {
          response = await (window as any).puter.ai.chat(
            promptOrMessages as string,
            imageURL,
            false, // Always use real AI when not in test mode
            { model, stream, tools }
          );
        } else if (Array.isArray(promptOrMessages)) {
          response = await (window as any).puter.ai.chat(
            promptOrMessages,
            false, // Always use real AI when not in test mode
            { model, stream, tools }
          );
        } else {
          response = await (window as any).puter.ai.chat(
            promptOrMessages,
            { model, stream, tools }
          );
        }

        // If we get a real response, enhance it with our features
        if (response && (response.message || response.content || response.text)) {
          console.log('✅ Real AI response received successfully');
          console.log('🔍 Response structure:', {
            hasMessage: !!response.message,
            hasContent: !!response.content,
            hasText: !!response.text,
            messageType: typeof response.message
          });
          return enhanceAIResponse(response, promptOrMessages);
        } else {
          console.log('⚠️ Real AI response was empty or invalid, trying alternatives...');
          console.log('🔍 Invalid response:', response);
        }
      } else {
        console.log('❌ Puter AI service not available, trying alternatives...');
      }

      // If no real AI available, try alternative AI services
      const alternativeResponse = await tryAlternativeAI(promptOrMessages, options);
      if (alternativeResponse) {
        return alternativeResponse;
      }

      // Final fallback to enhanced demo mode
      console.log('💡 No real AI available - using enhanced demo mode with intelligent responses...');
      return generateDemoResponse(promptOrMessages, options);

    } catch (err) {
      console.warn('AI service error, using intelligent fallback:', err);

      // Check if it's a network connectivity issue
      if (err instanceof Error && (
        err.message.includes('ERR_INTERNET_DISCONNECTED') ||
        err.message.includes('ERR_NETWORK') ||
        err.message.includes('Failed to fetch') ||
        err.message.includes('NetworkError')
      )) {
        console.log('🌐 Network connectivity issue detected, using offline mode');
        setError('Network connectivity issue. Using offline mode with enhanced responses.');
      }

      // Try alternative AI services on error
      try {
        const alternativeResponse = await tryAlternativeAI(promptOrMessages, options);
        if (alternativeResponse) {
          return alternativeResponse;
        }
      } catch (altErr) {
        console.warn('Alternative AI also failed:', altErr);
      }

      // Final fallback to demo mode
      return generateDemoResponse(promptOrMessages, options);
    } finally {
      if (!options.stream) {
        setIsLoading(false);
      }
    }
  }, []);

  // Enhance real AI responses with our features
  const enhanceAIResponse = (response: any, originalPrompt: string | ChatMessage[]) => {
    const prompt = typeof originalPrompt === 'string' ? originalPrompt :
                   originalPrompt[originalPrompt.length - 1]?.content || '';

    // Extract the actual message content
    let messageContent = '';
    if (typeof response.message === 'string') {
      messageContent = response.message;
    } else if (response.message?.content) {
      messageContent = response.message.content;
    } else if (response.message?.text) {
      messageContent = response.message.text;
    } else if (typeof response.message === 'object') {
      // Handle object messages by converting to string
      messageContent = JSON.stringify(response.message, null, 2);
    } else {
      messageContent = response.content || response.text || 'Response received';
    }

    // Check if the response should include enhanced features
    const shouldEnhance = prompt.toLowerCase().includes('animation') ||
                         prompt.toLowerCase().includes('interactive') ||
                         prompt.toLowerCase().includes('demo') ||
                         prompt.toLowerCase().includes('example') ||
                         prompt.toLowerCase().includes('molecular') ||
                         prompt.toLowerCase().includes('3d');

    if (shouldEnhance) {
      // Add enhanced features to real AI response
      return {
        ...response,
        message: messageContent + '\n\n' + getEnhancementSuggestion(prompt),
        enhanced: true
      };
    }

    // Return with properly formatted message
    return {
      ...response,
      message: messageContent
    };
  };

  // Try alternative AI services
  const tryAlternativeAI = async (promptOrMessages: string | ChatMessage[], options: any) => {
    // This could be expanded to include other AI services like OpenAI, Anthropic, etc.
    // For now, we'll return null to indicate no alternative service is available

    // Example structure for future implementation:
    /*
    try {
      if (window.openai) {
        const response = await window.openai.chat.completions.create({
          model: "gpt-3.5-turbo",
          messages: Array.isArray(promptOrMessages) ? promptOrMessages : [
            { role: "user", content: promptOrMessages }
          ]
        });
        return response.choices[0].message;
      }
    } catch (err) {
      console.warn('Alternative AI service failed:', err);
    }
    */

    return null;
  };

  // Get enhancement suggestions based on prompt
  const getEnhancementSuggestion = (prompt: string) => {
    if (prompt.toLowerCase().includes('molecular') || prompt.toLowerCase().includes('3d')) {
      return `\n🧪 **Enhanced Feature Available**: Try the 3D Molecular playground in the demo section to see interactive chemical animations!`;
    }
    if (prompt.toLowerCase().includes('animation') || prompt.toLowerCase().includes('css')) {
      return `\n✨ **Enhanced Feature Available**: Check out the Interactive playground for live CSS animation controls!`;
    }
    if (prompt.toLowerCase().includes('code') || prompt.toLowerCase().includes('html')) {
      return `\n💻 **Enhanced Feature Available**: Use the Live Code editor for real-time HTML/CSS/JS development!`;
    }
    return `\n🎯 **Enhanced Features Available**: Explore the demo section for interactive examples and live previews!`;
  };

  // Demo response generator for testing enhanced features
  const generateDemoResponse = async (
    promptOrMessages: string | ChatMessage[],
    options: UsePuterAIOptions & { testMode?: boolean } = {}
  ) => {
    // Extract the user's message
    let userMessage = '';
    if (Array.isArray(promptOrMessages)) {
      const lastMessage = promptOrMessages[promptOrMessages.length - 1];
      userMessage = lastMessage?.content || '';
    } else {
      userMessage = promptOrMessages;
    }

    // Generate enhanced demo responses based on keywords
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('3d') && lowerMessage.includes('svg') && (lowerMessage.includes('moon') || lowerMessage.includes('kid'))) {
      return generateMoonSVGDemo();
    } else if (lowerMessage.includes('3d') && (lowerMessage.includes('molecular') || lowerMessage.includes('chemical'))) {
      return generateMolecularDemo();
    } else if (lowerMessage.includes('html') || lowerMessage.includes('web') || lowerMessage.includes('website')) {
      return generateHTMLDemo();
    } else if (lowerMessage.includes('css') || lowerMessage.includes('animation')) {
      return generateCSSDemo();
    } else if (lowerMessage.includes('javascript') || lowerMessage.includes('interactive')) {
      return generateJavaScriptDemo();
    } else if (lowerMessage.includes('chart') || lowerMessage.includes('graph') || lowerMessage.includes('diagram')) {
      return generateDiagramDemo();
    } else {
      return generateBasicDemo(userMessage);
    }
  };

  // Demo response generators
  const generateMoonSVGDemo = () => {
    return {
      message: `# 🌙 3D SVG Midnight Moon with 12 Kids

Here's a beautiful 3D SVG animation of a midnight moon surrounded by 12 children:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Midnight Moon with 12 Kids</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 50%, #2d2d5f 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .scene {
            width: 800px;
            height: 800px;
            perspective: 1000px;
            position: relative;
        }

        .moon-container {
            width: 200px;
            height: 200px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transform-style: preserve-3d;
            animation: moonFloat 6s ease-in-out infinite;
        }

        .kids-circle {
            width: 600px;
            height: 600px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transform-style: preserve-3d;
            animation: kidsRotate 20s linear infinite;
        }

        .kid {
            position: absolute;
            width: 60px;
            height: 60px;
            transform-style: preserve-3d;
        }

        @keyframes moonFloat {
            0%, 100% { transform: translate(-50%, -50%) rotateY(0deg) translateZ(0px); }
            50% { transform: translate(-50%, -50%) rotateY(180deg) translateZ(20px); }
        }

        @keyframes kidsRotate {
            0% { transform: translate(-50%, -50%) rotateY(0deg); }
            100% { transform: translate(-50%, -50%) rotateY(360deg); }
        }

        @keyframes kidBounce {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-10px) scale(1.1); }
        }

        .stars {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 2s ease-in-out infinite alternate;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; transform: scale(1); }
            100% { opacity: 1; transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>

    <div class="scene">
        <!-- Moon -->
        <div class="moon-container">
            <svg width="200" height="200" viewBox="0 0 200 200">
                <defs>
                    <radialGradient id="moonGradient" cx="0.3" cy="0.3">
                        <stop offset="0%" style="stop-color:#fffacd;stop-opacity:1" />
                        <stop offset="70%" style="stop-color:#f0e68c;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#daa520;stop-opacity:1" />
                    </radialGradient>
                    <filter id="moonGlow">
                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>

                <!-- Moon body -->
                <circle cx="100" cy="100" r="80" fill="url(#moonGradient)" filter="url(#moonGlow)"/>

                <!-- Moon craters -->
                <ellipse cx="75" cy="80" rx="8" ry="6" fill="#daa520" opacity="0.6"/>
                <ellipse cx="120" cy="70" rx="5" ry="4" fill="#daa520" opacity="0.6"/>
                <ellipse cx="90" cy="120" rx="6" ry="5" fill="#daa520" opacity="0.6"/>
                <ellipse cx="130" cy="110" rx="4" ry="3" fill="#daa520" opacity="0.6"/>

                <!-- Moon face -->
                <circle cx="85" cy="85" r="3" fill="#daa520"/>
                <circle cx="115" cy="85" r="3" fill="#daa520"/>
                <path d="M 85 110 Q 100 120 115 110" stroke="#daa520" stroke-width="2" fill="none"/>
            </svg>
        </div>

        <!-- 12 Kids around the moon -->
        <div class="kids-circle">
            <!-- Kid 1 -->
            <div class="kid" style="transform: rotate(0deg) translateX(300px) rotate(-0deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#ffdbac"/>
                    <rect x="25" y="32" width="10" height="20" fill="#4169e1" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#ffdbac"/>
                    <rect x="35" y="35" width="5" height="12" fill="#ffdbac"/>
                </svg>
            </div>

            <!-- Kid 2 -->
            <div class="kid" style="transform: rotate(30deg) translateX(300px) rotate(-30deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#d4a574"/>
                    <rect x="25" y="32" width="10" height="20" fill="#ff69b4" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#d4a574"/>
                    <rect x="35" y="35" width="5" height="12" fill="#d4a574"/>
                </svg>
            </div>

            <!-- Kid 3 -->
            <div class="kid" style="transform: rotate(60deg) translateX(300px) rotate(-60deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#8b4513"/>
                    <rect x="25" y="32" width="10" height="20" fill="#32cd32" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#8b4513"/>
                    <rect x="35" y="35" width="5" height="12" fill="#8b4513"/>
                </svg>
            </div>

            <!-- Kid 4 -->
            <div class="kid" style="transform: rotate(90deg) translateX(300px) rotate(-90deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#ffdbac"/>
                    <rect x="25" y="32" width="10" height="20" fill="#ffa500" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#ffdbac"/>
                    <rect x="35" y="35" width="5" height="12" fill="#ffdbac"/>
                </svg>
            </div>

            <!-- Kid 5 -->
            <div class="kid" style="transform: rotate(120deg) translateX(300px) rotate(-120deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#f4a460"/>
                    <rect x="25" y="32" width="10" height="20" fill="#9370db" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#f4a460"/>
                    <rect x="35" y="35" width="5" height="12" fill="#f4a460"/>
                </svg>
            </div>

            <!-- Kid 6 -->
            <div class="kid" style="transform: rotate(150deg) translateX(300px) rotate(-150deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#deb887"/>
                    <rect x="25" y="32" width="10" height="20" fill="#dc143c" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#deb887"/>
                    <rect x="35" y="35" width="5" height="12" fill="#deb887"/>
                </svg>
            </div>

            <!-- Kid 7 -->
            <div class="kid" style="transform: rotate(180deg) translateX(300px) rotate(-180deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#cd853f"/>
                    <rect x="25" y="32" width="10" height="20" fill="#00ced1" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#cd853f"/>
                    <rect x="35" y="35" width="5" height="12" fill="#cd853f"/>
                </svg>
            </div>

            <!-- Kid 8 -->
            <div class="kid" style="transform: rotate(210deg) translateX(300px) rotate(-210deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#ffdbac"/>
                    <rect x="25" y="32" width="10" height="20" fill="#ff1493" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#ffdbac"/>
                    <rect x="35" y="35" width="5" height="12" fill="#ffdbac"/>
                </svg>
            </div>

            <!-- Kid 9 -->
            <div class="kid" style="transform: rotate(240deg) translateX(300px) rotate(-240deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#d2691e"/>
                    <rect x="25" y="32" width="10" height="20" fill="#228b22" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#d2691e"/>
                    <rect x="35" y="35" width="5" height="12" fill="#d2691e"/>
                </svg>
            </div>

            <!-- Kid 10 -->
            <div class="kid" style="transform: rotate(270deg) translateX(300px) rotate(-270deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#f5deb3"/>
                    <rect x="25" y="32" width="10" height="20" fill="#4b0082" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#f5deb3"/>
                    <rect x="35" y="35" width="5" height="12" fill="#f5deb3"/>
                </svg>
            </div>

            <!-- Kid 11 -->
            <div class="kid" style="transform: rotate(300deg) translateX(300px) rotate(-300deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#daa520"/>
                    <rect x="25" y="32" width="10" height="20" fill="#ff4500" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#daa520"/>
                    <rect x="35" y="35" width="5" height="12" fill="#daa520"/>
                </svg>
            </div>

            <!-- Kid 12 -->
            <div class="kid" style="transform: rotate(330deg) translateX(300px) rotate(-330deg);">
                <svg width="60" height="60" viewBox="0 0 60 60">
                    <circle cx="30" cy="20" r="12" fill="#bc8f8f"/>
                    <rect x="25" y="32" width="10" height="20" fill="#20b2aa" rx="2"/>
                    <circle cx="27" cy="17" r="1" fill="#000"/>
                    <circle cx="33" cy="17" r="1" fill="#000"/>
                    <path d="M 27 22 Q 30 24 33 22" stroke="#000" stroke-width="1" fill="none"/>
                    <rect x="20" y="35" width="5" height="12" fill="#bc8f8f"/>
                    <rect x="35" y="35" width="5" height="12" fill="#bc8f8f"/>
                </svg>
            </div>
        </div>
    </div>

    <script>
        // Create twinkling stars
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 100; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = Math.random() * 3 + 1 + 'px';
                star.style.height = star.style.width;
                star.style.animationDelay = Math.random() * 2 + 's';
                starsContainer.appendChild(star);
            }
        }

        // Add individual bounce animations to kids
        function animateKids() {
            const kids = document.querySelectorAll('.kid');
            kids.forEach((kid, index) => {
                kid.style.animation = \`kidBounce \${2 + Math.random() * 2}s ease-in-out infinite\`;
                kid.style.animationDelay = \`\${index * 0.2}s\`;
            });
        }

        // Initialize
        createStars();
        animateKids();

        // Add interactive hover effects
        document.querySelectorAll('.kid').forEach(kid => {
            kid.addEventListener('mouseenter', () => {
                kid.style.transform += ' scale(1.2)';
                kid.style.transition = 'transform 0.3s ease';
            });

            kid.addEventListener('mouseleave', () => {
                kid.style.transform = kid.style.transform.replace(' scale(1.2)', '');
            });
        });

        // Moon click interaction
        document.querySelector('.moon-container').addEventListener('click', () => {
            const moon = document.querySelector('.moon-container');
            moon.style.animation = 'moonFloat 1s ease-in-out';
            setTimeout(() => {
                moon.style.animation = 'moonFloat 6s ease-in-out infinite';
            }, 1000);
        });
    </script>
</body>
</html>
\`\`\`

## 🌟 **Features:**

- **3D Perspective**: Uses CSS 3D transforms for realistic depth
- **Animated Moon**: Floating and rotating with realistic glow effects
- **12 Diverse Kids**: Each child has unique skin tones and colorful clothes
- **Circular Motion**: Kids rotate around the moon in a perfect circle
- **Twinkling Stars**: Dynamic star field background
- **Interactive Elements**: Hover effects and click interactions
- **Responsive Design**: Adapts to different screen sizes

## 🎮 **Interactive Controls:**

- **Hover over kids** to see them grow larger
- **Click the moon** for a special animation
- **Stars twinkle** automatically in the background

This creates a magical midnight scene with a glowing moon surrounded by 12 happy children in a beautiful 3D SVG animation!`
    };
  };

  const generateMolecularDemo = () => {
    return {
      message: `# 🧬 Advanced 3D Molecular Animation System

I've created a comprehensive 3D molecular visualization system with interactive chemical reactions! This includes:

## 🎯 **Key Features:**

### 🔬 **3D Molecular Structures**
- **Realistic atomic representation** with accurate colors and sizes
- **Chemical bond visualization** (single, double, triple bonds)
- **3D rotation and perspective** with real-time rendering
- **Interactive controls** for zoom, rotation, and animation speed

### ⚗️ **Chemical Reaction Animations**
- **Multi-phase reactions** (reactants → transition → products)
- **Energy change visualization** with thermodynamic data
- **Molecular collision dynamics** during reaction phases
- **Real-time progress tracking** with phase indicators

### 🎮 **Interactive Playground**
- **Molecule library** with common chemical compounds
- **Reaction database** with various reaction types
- **Educational information** about molecular properties
- **Export functionality** for SVG animations

## 🧪 **Available Molecules:**
- **Water (H₂O)** - Bent molecular geometry
- **Carbon Dioxide (CO₂)** - Linear structure
- **Methane (CH₄)** - Tetrahedral geometry
- **Ammonia (NH₃)** - Trigonal pyramidal
- **Benzene (C₆H₆)** - Aromatic ring structure
- **Carbonic Acid (H₂CO₃)** - Complex acid structure

## ⚡ **Chemical Reactions:**
- **Water Formation:** 2H₂ + O₂ → 2H₂O
- **Carbonic Acid Formation:** H₂O + CO₂ → H₂CO₃
- **Methane Combustion:** CH₄ + 2O₂ → CO₂ + 2H₂O

<!-- MOLECULAR_PLAYGROUND -->

Here's a complete interactive example showing the carbonic acid formation reaction:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Molecular Reaction: H₂O + CO₂ → H₂CO₃</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow-x: auto;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .reaction-header {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .equation {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            color: #ffff00;
        }

        .molecule-stage {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
            margin: 40px 0;
            min-height: 300px;
        }

        .molecule-container {
            perspective: 1000px;
            margin: 20px;
        }

        .molecule-3d {
            transform-style: preserve-3d;
            animation: rotate3d 6s infinite linear;
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        .atom {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hydrogen {
            width: 24px;
            height: 24px;
            background: radial-gradient(circle at 30% 30%, #ffffff, #cccccc);
            color: #333;
        }

        .oxygen {
            width: 32px;
            height: 32px;
            background: radial-gradient(circle at 30% 30%, #ff4444, #cc0000);
            color: white;
        }

        .carbon {
            width: 28px;
            height: 28px;
            background: radial-gradient(circle at 30% 30%, #444444, #000000);
            color: white;
        }

        .bond {
            position: absolute;
            background: linear-gradient(90deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4));
            transform-origin: left center;
            border-radius: 1px;
        }

        .single-bond { height: 2px; }
        .double-bond { height: 4px; }

        /* Water molecule (H₂O) */
        .water .h1 { transform: translate3d(35px, 45px, 10px); }
        .water .o1 { transform: translate3d(50px, 60px, 0px); }
        .water .h2 { transform: translate3d(65px, 45px, -10px); }
        .water .bond1 {
            width: 20px;
            transform: translate3d(42px, 55px, 5px) rotate(-35deg);
        }
        .water .bond2 {
            width: 20px;
            transform: translate3d(58px, 55px, -5px) rotate(35deg);
        }

        /* CO₂ molecule */
        .co2 .c1 { transform: translate3d(50px, 60px, 0px); }
        .co2 .o1 { transform: translate3d(20px, 60px, 0px); }
        .co2 .o2 { transform: translate3d(80px, 60px, 0px); }
        .co2 .bond1 {
            width: 25px;
            transform: translate3d(25px, 58px, 0px);
        }
        .co2 .bond2 {
            width: 25px;
            transform: translate3d(55px, 58px, 0px);
        }

        /* Carbonic acid (H₂CO₃) */
        .carbonic .c1 { transform: translate3d(50px, 60px, 0px); }
        .carbonic .o1 { transform: translate3d(25px, 45px, 5px); }
        .carbonic .o2 { transform: translate3d(75px, 45px, -5px); }
        .carbonic .o3 { transform: translate3d(50px, 85px, 0px); }
        .carbonic .h1 { transform: translate3d(10px, 35px, 8px); }
        .carbonic .h2 { transform: translate3d(90px, 35px, -8px); }

        .molecule-label {
            margin-top: 20px;
            font-size: 18px;
            font-weight: bold;
        }

        .reaction-arrow {
            font-size: 40px;
            color: #ffff00;
            animation: pulse 2s infinite;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .controls {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .energy-display {
            background: rgba(255, 255, 0, 0.1);
            border: 2px solid #ffff00;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }

        @keyframes rotate3d {
            0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
            25% { transform: rotateX(90deg) rotateY(90deg) rotateZ(0deg); }
            50% { transform: rotateX(180deg) rotateY(180deg) rotateZ(90deg); }
            75% { transform: rotateX(270deg) rotateY(270deg) rotateZ(180deg); }
            100% { transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .phase-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .molecule-stage {
                flex-direction: column;
                gap: 20px;
            }
            .reaction-arrow {
                transform: rotate(90deg);
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="reaction-header">
            <h1>🧬 3D Molecular Reaction Animation</h1>
            <div class="equation">H₂O + CO₂ → H₂CO₃</div>
            <p>Watch as water and carbon dioxide combine to form carbonic acid</p>
        </div>

        <div class="energy-display">
            <strong>⚡ Energy Change (ΔH):</strong> -20 kJ/mol (Exothermic Reaction)
        </div>

        <div class="phase-indicator" id="phaseIndicator">
            Phase: Reactants
        </div>

        <div class="molecule-stage" id="reactionStage">
            <!-- Water molecule -->
            <div class="molecule-container" id="waterMolecule">
                <div class="molecule-3d water">
                    <div class="atom hydrogen h1">H</div>
                    <div class="atom oxygen o1">O</div>
                    <div class="atom hydrogen h2">H</div>
                    <div class="bond single-bond bond1"></div>
                    <div class="bond single-bond bond2"></div>
                </div>
                <div class="molecule-label">H₂O (Water)</div>
            </div>

            <div class="reaction-arrow" id="reactionArrow">
                +
            </div>

            <!-- CO₂ molecule -->
            <div class="molecule-container" id="co2Molecule">
                <div class="molecule-3d co2">
                    <div class="atom carbon c1">C</div>
                    <div class="atom oxygen o1">O</div>
                    <div class="atom oxygen o2">O</div>
                    <div class="bond double-bond bond1"></div>
                    <div class="bond double-bond bond2"></div>
                </div>
                <div class="molecule-label">CO₂ (Carbon Dioxide)</div>
            </div>

            <div class="reaction-arrow">
                →
            </div>

            <!-- Carbonic acid molecule -->
            <div class="molecule-container" id="carbonicMolecule" style="opacity: 0.3;">
                <div class="molecule-3d carbonic">
                    <div class="atom carbon c1">C</div>
                    <div class="atom oxygen o1">O</div>
                    <div class="atom oxygen o2">O</div>
                    <div class="atom oxygen o3">O</div>
                    <div class="atom hydrogen h1">H</div>
                    <div class="atom hydrogen h2">H</div>
                </div>
                <div class="molecule-label">H₂CO₃ (Carbonic Acid)</div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="startReaction()">🧪 Start Reaction</button>
            <button class="control-btn" onclick="pauseAnimation()">⏸️ Pause</button>
            <button class="control-btn" onclick="resetReaction()">🔄 Reset</button>
            <button class="control-btn" onclick="changeSpeed()">⚡ Speed: <span id="speedDisplay">1x</span></button>
            <button class="control-btn" onclick="toggleView()">👁️ Toggle View</button>
        </div>
    </div>

    <script>
        let reactionPhase = 'reactants';
        let animationSpeed = 1;
        let isPaused = false;
        let currentView = '3d';

        function startReaction() {
            const phases = ['reactants', 'transition', 'products'];
            let currentPhaseIndex = 0;

            const phaseInterval = setInterval(() => {
                if (isPaused) return;

                reactionPhase = phases[currentPhaseIndex];
                updatePhaseDisplay();
                updateMoleculeVisibility();

                currentPhaseIndex++;
                if (currentPhaseIndex >= phases.length) {
                    clearInterval(phaseInterval);
                    setTimeout(() => {
                        resetReaction();
                    }, 3000);
                }
            }, 2000 / animationSpeed);
        }

        function updatePhaseDisplay() {
            const indicator = document.getElementById('phaseIndicator');
            const phaseNames = {
                'reactants': 'Reactants (Initial State)',
                'transition': 'Transition State (Reaction Occurring)',
                'products': 'Products (Final State)'
            };
            indicator.textContent = \`Phase: \${phaseNames[reactionPhase]}\`;
        }

        function updateMoleculeVisibility() {
            const water = document.getElementById('waterMolecule');
            const co2 = document.getElementById('co2Molecule');
            const carbonic = document.getElementById('carbonicMolecule');
            const arrow = document.getElementById('reactionArrow');

            switch(reactionPhase) {
                case 'reactants':
                    water.style.opacity = '1';
                    co2.style.opacity = '1';
                    carbonic.style.opacity = '0.3';
                    arrow.textContent = '+';
                    break;
                case 'transition':
                    water.style.opacity = '0.7';
                    co2.style.opacity = '0.7';
                    carbonic.style.opacity = '0.7';
                    arrow.textContent = '⚡';
                    // Add glow effect
                    water.style.filter = 'drop-shadow(0 0 10px #ffff00)';
                    co2.style.filter = 'drop-shadow(0 0 10px #ffff00)';
                    break;
                case 'products':
                    water.style.opacity = '0.3';
                    co2.style.opacity = '0.3';
                    carbonic.style.opacity = '1';
                    arrow.textContent = '→';
                    // Remove glow effect
                    water.style.filter = 'none';
                    co2.style.filter = 'none';
                    break;
            }
        }

        function pauseAnimation() {
            isPaused = !isPaused;
            const molecules = document.querySelectorAll('.molecule-3d');
            molecules.forEach(mol => {
                mol.style.animationPlayState = isPaused ? 'paused' : 'running';
            });
        }

        function resetReaction() {
            reactionPhase = 'reactants';
            updatePhaseDisplay();
            updateMoleculeVisibility();

            const molecules = document.querySelectorAll('.molecule-3d');
            molecules.forEach(mol => {
                mol.style.animation = 'none';
                mol.offsetHeight; // Trigger reflow
                mol.style.animation = null;
            });
        }

        function changeSpeed() {
            const speeds = [0.5, 1, 2, 3];
            const currentIndex = speeds.indexOf(animationSpeed);
            animationSpeed = speeds[(currentIndex + 1) % speeds.length];

            document.getElementById('speedDisplay').textContent = \`\${animationSpeed}x\`;

            const molecules = document.querySelectorAll('.molecule-3d');
            molecules.forEach(mol => {
                mol.style.animationDuration = \`\${6 / animationSpeed}s\`;
            });
        }

        function toggleView() {
            currentView = currentView === '3d' ? '2d' : '3d';
            const molecules = document.querySelectorAll('.molecule-3d');

            if (currentView === '2d') {
                molecules.forEach(mol => {
                    mol.style.transform = 'none';
                    mol.style.animation = 'none';
                });
            } else {
                molecules.forEach(mol => {
                    mol.style.animation = \`rotate3d \${6 / animationSpeed}s infinite linear\`;
                });
            }
        }

        // Initialize
        updatePhaseDisplay();

        // Add click interactions
        document.querySelectorAll('.molecule-container').forEach(container => {
            container.addEventListener('click', function() {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 300);
            });
        });
    </script>
</body>
</html>
\`\`\`

## 🎯 **Advanced Features:**

### 🔬 **Scientific Accuracy:**
- **Realistic molecular geometry** based on VSEPR theory
- **Accurate bond lengths and angles** from experimental data
- **Proper atomic colors** following CPK convention
- **Thermodynamic data** including energy changes

### 🎮 **Interactive Controls:**
- **Multi-phase animation** with distinct reaction stages
- **Speed control** from 0.5x to 3x normal speed
- **Pause/resume** functionality for detailed observation
- **View toggle** between 2D and 3D perspectives
- **Click interactions** for molecule examination

### 📊 **Educational Value:**
- **Real chemical equations** with balanced stoichiometry
- **Energy change indicators** (exothermic/endothermic)
- **Phase descriptions** explaining reaction progress
- **Molecular property data** for each compound

This creates an immersive, educational experience for understanding chemical reactions at the molecular level!`
    };
  };

  const generateImage = useCallback(async (prompt: string, testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const image = await (window as any).puter.ai.txt2img(prompt, testMode);
      return image;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate image';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const transcribeImage = useCallback(async (image: string | File | Blob, testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const text = await (window as any).puter.ai.img2txt(image, testMode);
      return text;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to transcribe image';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateHTMLDemo = () => {
    return {
      message: `# 🌐 Interactive HTML Demo

I'll create a modern, responsive web component with interactive features:

\`\`\`html
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; color: white; text-align: center; max-width: 500px; margin: 20px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
    <h2 style="margin: 0 0 15px 0; font-size: 24px;">🎨 Interactive Card</h2>
    <p style="margin: 0 0 20px 0; opacity: 0.9;">Click the button to see magic happen!</p>
    <button onclick="changeColors()" style="background: #4CAF50; color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">✨ Change Colors</button>
    <div id="colorDisplay" style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px; font-family: monospace;">
        Current: #667eea → #764ba2
    </div>
</div>

<script>
function changeColors() {
    const colors = [
        ['#667eea', '#764ba2'],
        ['#f093fb', '#f5576c'],
        ['#4facfe', '#00f2fe'],
        ['#43e97b', '#38f9d7'],
        ['#fa709a', '#fee140']
    ];

    const randomPair = colors[Math.floor(Math.random() * colors.length)];
    const card = document.querySelector('div[style*="background: linear-gradient"]');
    const display = document.getElementById('colorDisplay');

    card.style.background = \`linear-gradient(135deg, \${randomPair[0]} 0%, \${randomPair[1]} 100%)\`;
    display.textContent = \`Current: \${randomPair[0]} → \${randomPair[1]}\`;

    // Add animation effect
    card.style.transform = 'scale(1.05)';
    setTimeout(() => {
        card.style.transform = 'scale(1)';
    }, 200);
}
</script>
\`\`\`

This creates a beautiful, interactive card with gradient backgrounds that change on click!`
    };
  };

  const generateCSSDemo = () => {
    return {
      message: `# 🎨 CSS Animation Demo

Here's an interactive CSS animation with controls:

<!-- INTERACTIVE:CSS_ANIMATION -->

\`\`\`css
.bouncing-ball {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  animation: bounce 2s infinite ease-in-out;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-50px) scale(1.1);
  }
}

.pulse-ring {
  width: 100px;
  height: 100px;
  border: 3px solid #4ecdc4;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
\`\`\`

Use the interactive controls above to adjust the animation properties and see how they affect the movement!`
    };
  };

  const generateJavaScriptDemo = () => {
    return {
      message: `# ⚡ Interactive JavaScript Demo

I'll create a dynamic color generator with interactive elements:

\`\`\`javascript
// Interactive Color Generator
function createColorfulDiv() {
  const div = document.createElement('div');
  const hue = Math.random() * 360;
  const saturation = Math.random() * 50 + 50;
  const lightness = Math.random() * 30 + 40;

  div.style.cssText = \`
    width: 80px;
    height: 80px;
    background: hsl(\${hue}, \${saturation}%, \${lightness}%);
    margin: 5px;
    border-radius: 10px;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  \`;

  div.addEventListener('click', () => {
    const newHue = Math.random() * 360;
    div.style.background = \`hsl(\${newHue}, \${saturation}%, \${lightness}%)\`;
    div.style.transform = 'scale(1.2)';
    setTimeout(() => {
      div.style.transform = 'scale(1)';
    }, 200);
  });

  div.addEventListener('mouseenter', () => {
    div.style.transform = 'scale(1.1) rotate(5deg)';
  });

  div.addEventListener('mouseleave', () => {
    div.style.transform = 'scale(1) rotate(0deg)';
  });

  return div;
}

// Create initial set of colorful divs
const container = document.createElement('div');
container.style.cssText = 'text-align: center; padding: 20px;';

for (let i = 0; i < 8; i++) {
  container.appendChild(createColorfulDiv());
}

// Add control button
const addButton = document.createElement('button');
addButton.textContent = '+ Add More Colors';
addButton.style.cssText = \`
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin: 10px;
  font-size: 16px;
\`;

addButton.addEventListener('click', () => {
  container.insertBefore(createColorfulDiv(), addButton);
});

container.appendChild(addButton);
document.body.appendChild(container);
\`\`\`

**Try it out!** Click on any colored square to change its color, or hover to see animations!`
    };
  };

  const generateDiagramDemo = () => {
    return {
      message: `# 📊 Visual Diagram Demo

Here's an interactive flowchart showing a web development process:

<!-- DIAGRAM -->

\`\`\`mermaid
graph TD
    A[Start Project] --> B{Choose Framework}
    B -->|React| C[Setup React App]
    B -->|Vue| D[Setup Vue App]
    B -->|Angular| E[Setup Angular App]
    C --> F[Install Dependencies]
    D --> F
    E --> F
    F --> G[Design Components]
    G --> H[Implement Features]
    H --> I{Testing}
    I -->|Pass| J[Deploy]
    I -->|Fail| K[Fix Bugs]
    K --> H
    J --> L[Monitor & Maintain]
\`\`\`

This diagram shows the typical workflow for web development projects with decision points and feedback loops.`
    };
  };

  const generateBasicDemo = (userMessage: string) => {
    return {
      message: `# 💬 Enhanced Response Demo

Thank you for your message: "${userMessage}"

I'm currently running in **demo mode** to showcase the enhanced response features. Here are some examples you can try:

## 🎯 **Try These Commands:**

### For 3D Molecular Animations:
- "Create a 3D animation for chemical molecular reactions"
- "Show me 3D SVG code for molecular structures"

### For Interactive HTML:
- "Create an interactive HTML example"
- "Build a responsive web component"

### For CSS Animations:
- "Show me CSS animation examples"
- "Create animated elements with CSS"

### For JavaScript Demos:
- "Build an interactive JavaScript demo"
- "Create a dynamic color generator"

### For Visual Diagrams:
- "Create a flowchart diagram"
- "Show me a process visualization"

## ✨ **Enhanced Features Available:**

1. **Live Code Previews** - See HTML, CSS, and JavaScript in action
2. **Interactive Demonstrations** - Adjust parameters in real-time
3. **3D Molecular Animations** - Scientific visualizations
4. **Visual Diagrams** - Flowcharts and process maps
5. **Rich Formatting** - Beautiful typography and layouts

Try any of the suggested commands above to see these features in action!`
    };
  };

  const textToSpeech = useCallback(async (text: string, language = 'en-US', testMode = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const audio = await (window as any).puter.ai.txt2speech(text, language, testMode);
      return audio;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate speech';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    sendMessage,
    generateImage,
    transcribeImage,
    textToSpeech,
    isLoading,
    error,
    setIsLoading,
  };
};
